"""
Manual BlendPro initialization script
Run this in Blender's Text Editor to manually initialize BlendPro AI
"""

import bpy
import sys
from pathlib import Path

def manual_blendpro_initialize():
    """Manually initialize BlendPro AI system"""
    
    print("=" * 60)
    print("Manual BlendPro AI Initialization")
    print("=" * 60)
    
    # Step 1: Add lib directory to path
    try:
        addon_dir = Path(__file__).parent
        lib_dir = addon_dir / "lib"
        
        if lib_dir.exists():
            lib_path_str = str(lib_dir)
            if lib_path_str not in sys.path:
                sys.path.insert(0, lib_path_str)
                print(f"✓ Added lib directory: {lib_path_str}")
        else:
            print(f"✗ Lib directory not found: {lib_dir}")
            return False
    except Exception as e:
        print(f"✗ Failed to add lib directory: {e}")
        return False
    
    # Step 2: Get addon preferences
    try:
        addon_name = "BlendProV2"
        if addon_name not in bpy.context.preferences.addons:
            print(f"✗ {addon_name} addon is not enabled")
            return False
            
        addon_prefs = bpy.context.preferences.addons[addon_name].preferences
        print("✓ Got addon preferences")
        
        # Check API key
        api_key = getattr(addon_prefs, 'api_key', '')
        if not api_key:
            print("✗ No API key configured!")
            print("  → Please set your OpenAI API key in addon preferences")
            return False
        
        print(f"✓ API key configured (length: {len(api_key)})")
        
    except Exception as e:
        print(f"✗ Failed to get preferences: {e}")
        return False
    
    # Step 3: Import and sync settings
    try:
        from BlendProV2.config.settings import sync_from_preferences, get_settings
        
        # Sync settings with preferences
        sync_from_preferences(addon_prefs)
        settings = get_settings()
        
        print("✓ Settings synchronized with preferences")
        print(f"  - API Key: {'Set' if settings.api_key else 'Not set'}")
        print(f"  - Temperature: {settings.temperature}")
        print(f"  - Max Tokens: {settings.max_tokens}")
        
    except Exception as e:
        print(f"✗ Failed to sync settings: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 4: Initialize API client
    try:
        from BlendProV2.utils.api_client import get_api_client
        
        api_client = get_api_client()
        print("✓ API client initialized")
        
    except Exception as e:
        print(f"✗ Failed to initialize API client: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 5: Test API connection
    try:
        print("Testing API connection...")
        
        test_result = api_client.test_connection()
        
        if test_result["success"]:
            print("✓ API connection successful!")
            print(f"  - Model: {test_result.get('model', 'unknown')}")
            print(f"  - Response: {test_result.get('content', '')[:50]}...")
        else:
            print(f"✗ API connection failed: {test_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"✗ API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("=" * 60)
    print("🎉 BlendPro AI successfully initialized!")
    print("=" * 60)
    return True

def check_operator_registration():
    """Check if BlendPro operators are properly registered"""
    
    print("\n" + "=" * 40)
    print("Checking Operator Registration")
    print("=" * 40)
    
    # Check if blendpro operators exist
    if hasattr(bpy.ops, 'blendpro'):
        print("✓ blendpro operators namespace exists")
        
        # Check specific operators
        operators_to_check = [
            'initialize_ai',
            'test_api_connection',
            'clear_cache',
            'reset_settings'
        ]
        
        for op_name in operators_to_check:
            if hasattr(bpy.ops.blendpro, op_name):
                print(f"✓ blendpro.{op_name} is registered")
            else:
                print(f"✗ blendpro.{op_name} is NOT registered")
    else:
        print("✗ blendpro operators namespace does not exist")
        print("  → This means the addon UI module failed to register")

if __name__ == "__main__":
    # First check operator registration
    check_operator_registration()
    
    # Then try manual initialization
    manual_blendpro_initialize()
