"""
Manual addon reload script for BlendPro
Run this in Blender's Text Editor to force reload the addon
"""

import bpy
import sys
import importlib

def reload_blendpro_addon():
    """Force reload BlendPro addon"""
    
    addon_name = "BlendProV2"
    
    print("=" * 50)
    print("Reloading BlendPro Addon")
    print("=" * 50)
    
    # Disable addon if enabled
    if addon_name in bpy.context.preferences.addons:
        print(f"Disabling {addon_name}...")
        bpy.ops.preferences.addon_disable(module=addon_name)
        print("✓ Addon disabled")
    
    # Remove from sys.modules to force reimport
    modules_to_remove = []
    for module_name in sys.modules.keys():
        if module_name.startswith(addon_name):
            modules_to_remove.append(module_name)
    
    for module_name in modules_to_remove:
        print(f"Removing module: {module_name}")
        del sys.modules[module_name]
    
    print(f"✓ Removed {len(modules_to_remove)} modules from cache")
    
    # Re-enable addon
    try:
        print(f"Enabling {addon_name}...")
        bpy.ops.preferences.addon_enable(module=addon_name)
        print("✓ Addon enabled successfully")
        
        # Check if addon is properly loaded
        if addon_name in bpy.context.preferences.addons:
            addon = bpy.context.preferences.addons[addon_name]
            prefs = addon.preferences
            print("✓ Addon preferences accessible")
            
            # Check if our new operator is registered
            if hasattr(bpy.ops, 'blendpro') and hasattr(bpy.ops.blendpro, 'initialize_ai'):
                print("✓ Initialize AI operator is registered")
            else:
                print("✗ Initialize AI operator not found")
                
        else:
            print("✗ Addon not found in preferences")
            
    except Exception as e:
        print(f"✗ Failed to enable addon: {e}")
        return False
    
    print("=" * 50)
    print("Reload complete! Check Preferences > Add-ons > BlendPro")
    print("=" * 50)
    return True

if __name__ == "__main__":
    reload_blendpro_addon()
